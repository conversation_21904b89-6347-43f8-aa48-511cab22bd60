# Analysis Results

This directory contains the results of the federated learning experiments, organized by task type.

## Directory Structure

- `classification/`: Contains results from classification tasks
  - `centralized/`: Centralized training results
  - `federated/`: Federated learning results
  - `comparison/`: Comparison between centralized and federated results

- `generation/`: Contains results from text generation tasks
  - `centralized/`: Centralized training results
  - `federated/`: Federated learning results
  - `comparison/`: Comparison between centralized and federated results

## Notes

- All metrics are stored in CSV format
- LaTeX tables are provided for easy inclusion in papers
- The `comparison` directories contain combined results and analysis
