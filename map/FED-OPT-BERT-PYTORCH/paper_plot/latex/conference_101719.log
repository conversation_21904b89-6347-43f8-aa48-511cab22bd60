This is pdfTeX, Version 3.14159265-2.6-1.40.20 (TeX Live 2019/Debian) (preloaded format=pdflatex 2025.5.16)  29 AUG 2025 17:09
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**conference_101719.tex
(./conference_101719.tex
LaTeX2e <2020-02-02> patch level 2
L3 programming layer <2020-02-14>
(/usr/share/texlive/texmf-dist/tex/latex/IEEEtran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen134
\@IEEEtrantmpdimenB=\dimen135
\@IEEEtrantmpdimenC=\dimen136
\@IEEEtrantmpcountA=\count167
\@IEEEtrantmpcountB=\count168
\@IEEEtrantmpcountC=\count169
\@IEEEtrantmptoksA=\toks14
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 5
03.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen137
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen138
\CLASSINFOnormalsizeunitybaselineskip=\dimen139
\IEEEnormaljot=\dimen140
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

\IEEEquantizedlength=\dimen141
\IEEEquantizedlengthdiff=\dimen142
\IEEEquantizedtextheightdiff=\dimen143
\IEEEilabelindentA=\dimen144
\IEEEilabelindentB=\dimen145
\IEEEilabelindent=\dimen146
\IEEEelabelindent=\dimen147
\IEEEdlabelindent=\dimen148
\IEEElabelindent=\dimen149
\IEEEiednormlabelsep=\dimen150
\IEEEiedmathlabelsep=\dimen151
\IEEEiedtopsep=\skip47
\c@section=\count170
\c@subsection=\count171
\c@subsubsection=\count172
\c@paragraph=\count173
\c@IEEEsubequation=\count174
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\c@figure=\count175
\c@table=\count176
\@IEEEeqnnumcols=\count177
\@IEEEeqncolcnt=\count178
\@IEEEsubeqnnumrollback=\count179
\@IEEEquantizeheightA=\dimen152
\@IEEEquantizeheightB=\dimen153
\@IEEEquantizeheightC=\dimen154
\@IEEEquantizeprevdepth=\dimen155
\@IEEEquantizemultiple=\count180
\@IEEEquantizeboxA=\box45
\@IEEEtmpitemindent=\dimen156
\IEEEPARstartletwidth=\dimen157
\c@IEEEbiography=\count181
\@IEEEtranrubishbin=\box46
)
** ATTENTION: Overriding command lockouts (line 2).
(/usr/share/texlive/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2020/01/20 v2.17e AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2000/06/29 v2.01 AMS text

(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks15
\ex@=\dimen158
))
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen159
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2016/03/08 v2.02 operator names
)
\inf@bad=\count182
LaTeX Info: Redefining \frac on input line 227.
\uproot@=\count183
\leftroot@=\count184
LaTeX Info: Redefining \overline on input line 389.
\classnum@=\count185
\DOTSCASE@=\count186
LaTeX Info: Redefining \ldots on input line 486.
LaTeX Info: Redefining \dots on input line 489.
LaTeX Info: Redefining \cdots on input line 610.
\Mathstrutbox@=\box47
\strutbox@=\box48
\big@size=\dimen160
LaTeX Font Info:    Redeclaring font encoding OML on input line 733.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 734.
\macc@depth=\count187
\c@MaxMatrixCols=\count188
\dotsspace@=\muskip16
\c@parentequation=\count189
\dspbrk@lvl=\count190
\tag@help=\toks16
\row@=\count191
\column@=\count192
\maxfields@=\count193
\andhelp@=\toks17
\eqnshift@=\dimen161
\alignsep@=\dimen162
\tagshift@=\dimen163
\tagwidth@=\dimen164
\totwidth@=\dimen165
\lineht@=\dimen166
\@envbody=\toks18
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks19
LaTeX Info: Redefining \[ on input line 2859.
LaTeX Info: Redefining \] on input line 2860.
)
(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/share/texlive/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'

(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2014/09/29 v1.1c Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
\c@ALC@unique=\count194
\c@ALC@line=\count195
\c@ALC@rem=\count196
\c@ALC@depth=\count197
\ALC@tlm=\skip53
\algorithmicindent=\skip54
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2019/11/30 v1.2a Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2019/11/30 v1.4a Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 105.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2018/01/08 v1.0l Graphics/color driver for pdftex
))
\Gin@req@height=\dimen167
\Gin@req@width=\dimen168
)
(/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1352.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
)
(/usr/share/texlive/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count198
\float@exts=\toks21
\float@box=\box49
\@float@everytoks=\toks22
\@floatcapt=\box50
)
(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdfmode.def
File: l3backend-pdfmode.def 2020-02-03 L3 backend support: PDF mode
\l__kernel_color_stack_int=\count199
\l__pdf_internal_box=\box51
)
(./conference_101719.aux)
\openout1 = `conference_101719.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.

-- Lines per column: 56 (exact).
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count266
\scratchdimen=\dimen169
\scratchbox=\box52
\nofMPsegments=\count267
\nofMParguments=\count268
\everyMPshowfont=\toks23
\MPscratchCnt=\count269
\MPscratchDim=\dimen170
\MPnumerator=\count270
\makeMPintoPDFobject=\count271
\everyMPtoPDFconversion=\toks24
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
LaTeX Font Info:    Trying to load font information for U+msa on input line 58.


(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 58.


(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Calculating math sizes for size <11> on input line 58.
 [1{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}


]
LaTeX Font Info:    Trying to load font information for OT1+pcr on input line 1
28.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1pcr.fd
File: ot1pcr.fd 2001/06/04 font definitions for OT1/pcr.
) [2]
Overfull \hbox (9.58073pt too wide) in paragraph at lines 231--246
 [][] 
 []


Overfull \hbox (9.58073pt too wide) in paragraph at lines 250--265
 [][] 
 []


Overfull \hbox (9.58073pt too wide) in paragraph at lines 276--291
 [][] 
 []


Overfull \hbox (9.58073pt too wide) in paragraph at lines 295--310
 [][] 
 []


Underfull \hbox (badness 10000) in paragraph at lines 327--328
[]\OT1/ptm/b/n/10 Model Spec-i-fi-ca-tions: \OT1/ptm/m/n/10 BART-large uses
 []


Underfull \hbox (badness 1448) in paragraph at lines 327--328
\OT1/ptm/m/n/10 Dis-til-BART uses the dis-tilled vari-ant (66M pa-ram-e-ters).
 []


Underfull \hbox (badness 10000) in paragraph at lines 327--328
\OT1/ptm/m/n/10 Both mod-els pro-cess CNN/DailyMail dataset with
 []


Underfull \hbox (badness 1360) in paragraph at lines 327--328
\OT1/ptm/m/n/10 train/validation/test splits, us-ing null sam-pling lim-its for

 []


Underfull \hbox (badness 10000) in paragraph at lines 334--335
\OT1/ptm/m/n/10 dis-tilled encoder--decoder) against BART-large (higher-
 []


Underfull \hbox (badness 6094) in paragraph at lines 334--335
\OT1/ptm/m/n/10 client counts. Dis-til-BART ex-per-i-ments cover Dirich-let
 []

<../plots/classification/performance_comparison_accuracy.png, id=18, 709.6914pt
 x 348.3414pt>
File: ../plots/classification/performance_comparison_accuracy.png Graphic file 
(type png)
<use ../plots/classification/performance_comparison_accuracy.png>
Package pdftex.def Info: ../plots/classification/performance_comparison_accurac
y.png  used on input line 346.
(pdftex.def)             Requested size: 252.0pt x 123.68597pt.
<../plots/classification/performance_comparison_f1.png, id=20, 709.6914pt x 348
.3414pt>
File: ../plots/classification/performance_comparison_f1.png Graphic file (type 
png)
<use ../plots/classification/performance_comparison_f1.png>
Package pdftex.def Info: ../plots/classification/performance_comparison_f1.png 
 used on input line 354.
(pdftex.def)             Requested size: 252.0pt x 123.68597pt.
<../plots/classification/fed_vs_central_progress.png, id=21, 854.2314pt x 276.0
714pt>
File: ../plots/classification/fed_vs_central_progress.png Graphic file (type pn
g)
<use ../plots/classification/fed_vs_central_progress.png>
Package pdftex.def Info: ../plots/classification/fed_vs_central_progress.png  u
sed on input line 362.
(pdftex.def)             Requested size: 252.0pt x 81.44035pt.

Overfull \hbox (16.98851pt too wide) in paragraph at lines 373--381
 [][] 
 []


Underfull \vbox (badness 10000) has occurred while \output is active []

 [3]
Underfull \vbox (badness 10000) has occurred while \output is active []


Underfull \vbox (badness 10000) has occurred while \output is active []

 [4 <../plots/classification/performance_comparison_accuracy.png> <../plots/cla
ssification/performance_comparison_f1.png>]
<../plots/generation/performance_comparison_rouge1.png, id=34, 709.6914pt x 348
.3414pt>
File: ../plots/generation/performance_comparison_rouge1.png Graphic file (type 
png)
<use ../plots/generation/performance_comparison_rouge1.png>
Package pdftex.def Info: ../plots/generation/performance_comparison_rouge1.png 
 used on input line 452.
(pdftex.def)             Requested size: 247.6778pt x 121.56519pt.
<../plots/generation/performance_comparison_bleu4.png, id=35, 709.6914pt x 348.
3414pt>
File: ../plots/generation/performance_comparison_bleu4.png Graphic file (type p
ng)
<use ../plots/generation/performance_comparison_bleu4.png>
Package pdftex.def Info: ../plots/generation/performance_comparison_bleu4.png  
used on input line 457.
(pdftex.def)             Requested size: 247.6778pt x 121.56519pt.
<../plots/classification/cls_f1_vs_clients_combined.png, id=36, 637.4214pt x 34
8.3414pt>
File: ../plots/classification/cls_f1_vs_clients_combined.png Graphic file (type
 png)
<use ../plots/classification/cls_f1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/classification/cls_f1_vs_clients_combined.png
  used on input line 504.
(pdftex.def)             Requested size: 252.0pt x 137.71294pt.

Underfull \hbox (badness 1845) in paragraph at lines 518--519
 \OT1/ptm/m/it/10 c) Find-ings.: [][] \OT1/ptm/m/n/10 Text gen-er-a-tion re-sul
ts re-veal dis-tinct
 []

<../plots/generation/rouge1_vs_clients_combined.png, id=37, 637.4214pt x 348.34
14pt>
File: ../plots/generation/rouge1_vs_clients_combined.png Graphic file (type png
)
<use ../plots/generation/rouge1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/rouge1_vs_clients_combined.png  us
ed on input line 524.
(pdftex.def)             Requested size: 252.0pt x 137.71294pt.
[5 <../plots/classification/fed_vs_central_progress.png> <../plots/generation/p
erformance_comparison_rouge1.png> <../plots/generation/performance_comparison_b
leu4.png> <../plots/classification/cls_f1_vs_clients_combined.png>]
File: ../plots/classification/cls_f1_vs_clients_combined.png Graphic file (type
 png)
<use ../plots/classification/cls_f1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/classification/cls_f1_vs_clients_combined.png
  used on input line 555.
(pdftex.def)             Requested size: 252.0pt x 137.71294pt.

Underfull \vbox (badness 10000) has occurred while \output is active []

 [6 <../plots/generation/rouge1_vs_clients_combined.png>]
File: ../plots/generation/rouge1_vs_clients_combined.png Graphic file (type png
)
<use ../plots/generation/rouge1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/rouge1_vs_clients_combined.png  us
ed on input line 567.
(pdftex.def)             Requested size: 247.6778pt x 135.35295pt.
<../plots/generation/bleu4_vs_clients_combined.png, id=51, 637.1805pt x 348.341
4pt>
File: ../plots/generation/bleu4_vs_clients_combined.png Graphic file (type png)

<use ../plots/generation/bleu4_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/bleu4_vs_clients_combined.png  use
d on input line 572.
(pdftex.def)             Requested size: 247.6778pt x 135.4008pt.
<../analysis_results/classification/federated/client_distributions/overall_clas
s_distribution.png, id=52, 1076.823pt x 569.9694pt>
File: ../analysis_results/classification/federated/client_distributions/overall
_class_distribution.png Graphic file (type png)
<use ../analysis_results/classification/federated/client_distributions/overall_
class_distribution.png>
Package pdftex.def Info: ../analysis_results/classification/federated/client_di
stributions/overall_class_distribution.png  used on input line 594.
(pdftex.def)             Requested size: 252.0pt x 133.37752pt.
<../analysis_results/classification/federated/client_distributions/client_share
_stacked_2_5.png, id=53, 860.013pt x 425.4294pt>
File: ../analysis_results/classification/federated/client_distributions/client_
share_stacked_2_5.png Graphic file (type png)
<use ../analysis_results/classification/federated/client_distributions/client_s
hare_stacked_2_5.png>
Package pdftex.def Info: ../analysis_results/classification/federated/client_di
stributions/client_share_stacked_2_5.png  used on input line 604.
(pdftex.def)             Requested size: 247.6778pt x 122.52097pt.
<../analysis_results/classification/federated/client_distributions/client_share
_stacked_2_10.png, id=54, 860.013pt x 425.4294pt>
File: ../analysis_results/classification/federated/client_distributions/client_
share_stacked_2_10.png Graphic file (type png)
<use ../analysis_results/classification/federated/client_distributions/client_s
hare_stacked_2_10.png>
Package pdftex.def Info: ../analysis_results/classification/federated/client_di
stributions/client_share_stacked_2_10.png  used on input line 609.
(pdftex.def)             Requested size: 247.6778pt x 122.52097pt.

Underfull \vbox (badness 10000) has occurred while \output is active []

 [7 <../plots/generation/bleu4_vs_clients_combined.png> <../analysis_results/cl
assification/federated/client_distributions/overall_class_distribution.png>]
File: ../plots/generation/rouge1_vs_clients_combined.png Graphic file (type png
)
<use ../plots/generation/rouge1_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/rouge1_vs_clients_combined.png  us
ed on input line 640.
(pdftex.def)             Requested size: 247.6778pt x 135.35295pt.
File: ../plots/generation/bleu4_vs_clients_combined.png Graphic file (type png)

<use ../plots/generation/bleu4_vs_clients_combined.png>
Package pdftex.def Info: ../plots/generation/bleu4_vs_clients_combined.png  use
d on input line 645.
(pdftex.def)             Requested size: 247.6778pt x 135.4008pt.
 [8 <../analysis_results/classification/federated/client_distributions/client_s
hare_stacked_2_5.png> <../analysis_results/classification/federated/client_dist
ributions/client_share_stacked_2_10.png>]

** Conference Paper **
Before submitting the final camera ready copy, remember to:

 1. Manually equalize the lengths of two columns on the last page
 of your paper;

 2. Ensure that any PostScript and/or PDF output post-processing
 uses only Type 1 fonts and that every step in the generation
 process uses the appropriate paper size.

[9] (./conference_101719.aux)

LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 4092 strings out of 481239
 61697 string characters out of 5920377
 323051 words of memory out of 5000000
 19294 multiletter control sequences out of 15000+600000
 585191 words of font info for 127 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 30i,11n,37p,1099b,326s stack positions out of 5000i,500n,10000p,200000b,80000s
{/usr/share/texlive/texmf-dist/fonts/enc/dvips/base/8r.enc}</usr/share/texliv
e/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texlive/texm
f-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/share/texlive/texmf-dist/
fonts/type1/public/amsfonts/cm/cmmi9.pfb></usr/share/texlive/texmf-dist/fonts/t
ype1/public/amsfonts/cm/cmr10.pfb></usr/share/texlive/texmf-dist/fonts/type1/pu
blic/amsfonts/cm/cmr7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/ams
fonts/cm/cmr8.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm
/cmr9.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.
pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></u
sr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/shar
e/texlive/texmf-dist/fonts/type1/urw/courier/ucrr8a.pfb></usr/share/texlive/tex
mf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/share/texlive/texmf-dist/fonts/t
ype1/urw/times/utmbi8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times
/utmr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on conference_101719.pdf (9 pages, 1718287 bytes).
PDF statistics:
 118 PDF objects out of 1000 (max. 8388607)
 69 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 56 words of extra memory for PDF output out of 10000 (max. 10000000)

