#!/usr/bin/env python3
"""
Script to compile LaTeX document using Python LaTeX package
"""

import os
import sys
from latex import build_pdf

def compile_latex_document(tex_file):
    """Compile LaTeX document to PDF"""
    try:
        # Read the LaTeX file
        with open(tex_file, 'r', encoding='utf-8') as f:
            latex_content = f.read()
        
        print(f"Compiling {tex_file}...")
        
        # Build PDF
        pdf = build_pdf(latex_content)
        
        # Save PDF
        pdf_filename = tex_file.replace('.tex', '.pdf')
        with open(pdf_filename, 'wb') as f:
            f.write(pdf)
        
        print(f"Successfully compiled to {pdf_filename}")
        return True
        
    except Exception as e:
        print(f"Error compiling LaTeX: {e}")
        return False

if __name__ == "__main__":
    tex_file = "conference_101719.tex"
    if len(sys.argv) > 1:
        tex_file = sys.argv[1]
    
    if not os.path.exists(tex_file):
        print(f"Error: {tex_file} not found")
        sys.exit(1)
    
    success = compile_latex_document(tex_file)
    sys.exit(0 if success else 1)
