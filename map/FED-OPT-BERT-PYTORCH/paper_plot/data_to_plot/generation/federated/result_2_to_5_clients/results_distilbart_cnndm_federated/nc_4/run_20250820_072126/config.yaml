algorithm: fedavg
batch_size: 8
beta1: 0.9
beta2: 0.999
client_lr: 5e-5
clients_per_round: 2
data_dir: ./data/cnndm
dirichlet_alpha: 0.5
early_stopping: true
early_stopping_patience: 3
eval_interval: 1
eval_steps: 500
gradient_accumulation_steps: 4
learning_rate: 5e-5
length_penalty: 2.0
local_epochs: 1
log_interval: 10
logging_steps: 100
max_grad_norm: 1.0
max_source_length: 256
max_target_length: 64
max_test_samples: 5000
max_train_samples: 10000
max_val_samples: 5000
model_name: facebook/bart-large-cnn
momentum: 0.9
no_repeat_ngram_size: 3
num_beams: 4
num_clients: 4
num_rounds: 5
output_dir: ./results_distilbart_cnndm_federated
save_interval: 1
save_model: false
save_model_freq: 1
save_steps: 1000
seed: 42
server_lr: 1.0
tau: 1e-3
test_split: test
train_split: train
use_cpu: false
use_distilbart: true
use_wandb: false
val_split: validation
warmup_steps: 100
weight_decay: 0.01
